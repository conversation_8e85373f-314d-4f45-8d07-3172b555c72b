import { type TFunction } from 'i18next';
import { type ReactNode } from 'react';
import { type HeaderIconConfig } from '~/components/Nav/Header';
import { type NavTabItem, type DropdownItem } from '~/common/types';
import type * as t from 'librechat-data-provider';

export const NAV_TABS: NavTabItem[] = [
  {
    id: 'chat',
    icon: '/assets/dlt_icon_deploy.svg',
    textKey: 'com_nav_chat',
  },
];

interface CreateHeaderIconsParams {
  t: TFunction;
  user: t.TUser | null;
  isProfileDropdownOpen: boolean;
  setIsProfileDropdownOpen: (open: boolean) => void;
  handleHelpClick: () => void;
  handleNotificationClick: () => void;
  handleUserProfile: () => void;
  logout: () => void;
  profileDropdownHeader: ReactNode;
}

export const createProfileDropdownItems = (
  _t: TFunction,
  handleUserProfile: () => void,
  logout: () => void,
): DropdownItem[] => [
  {
    id: 'profile',
    icon: '/assets/dlt_icon_profile.svg',
    textKey: 'com_nav_user_profile',
    onClick: handleUserProfile,
  },
  {
    id: 'separator1',
    textKey: '',
    onClick: () => {},
    type: 'separator' as const,
  },
  {
    id: 'logout',
    icon: '/assets/dlt_icon_signout.svg',
    textKey: 'com_nav_logout',
    onClick: logout,
  },
];

export const createHeaderIconsConfig = ({
  t,
  isProfileDropdownOpen,
  setIsProfileDropdownOpen,
  handleHelpClick,
  handleNotificationClick,
  handleUserProfile,
  logout,
  profileDropdownHeader,
}: CreateHeaderIconsParams): HeaderIconConfig[] => {
  const profileDropdownItems = createProfileDropdownItems(t, handleUserProfile, logout);

  return [
    {
      id: 'help',
      icon: '/assets/dlt_icon_help.svg',
      ariaLabel: t('com_nav_help'),
      showDropdown: false,
      onClick: handleHelpClick,
    },
    {
      id: 'notification',
      icon: '/assets/dlt_icon_notification.svg',
      ariaLabel: t('com_nav_notifications'),
      showDropdown: false,
      onClick: handleNotificationClick,
    },
    {
      id: 'profile',
      icon: '/assets/dlt_icon_profile.svg',
      ariaLabel: t('com_nav_profile'),
      showDropdown: true,
      dropdownItems: profileDropdownItems,
      dropdownHeader: profileDropdownHeader,
      isOpen: isProfileDropdownOpen,
      onOpenChange: setIsProfileDropdownOpen,
    },
  ];
};