import { type TFunction } from 'i18next';
import { type HeaderIconConfig } from '~/components/Nav/HeaderIcons';
import { type User } from '~/common/types';
import { createProfileDropdownItems } from './profileUtils';
import { type ReactNode } from 'react';

interface CreateHeaderIconsParams {
  t: TFunction;
  user: User | null;
  isProfileDropdownOpen: boolean;
  setIsProfileDropdownOpen: (open: boolean) => void;
  handleHelpClick: () => void;
  handleNotificationClick: () => void;
  handleUserProfile: () => void;
  handleLogout: () => void;
  profileDropdownHeader: ReactNode;
}

/**
 * Creates header icons configuration
 */
export const createHeaderIconsConfig = ({
  t,
  user,
  isProfileDropdownOpen,
  setIsProfileDropdownOpen,
  handleHelpClick,
  handleNotificationClick,
  handleUserProfile,
  handleLogout,
  profileDropdownHeader,
}: CreateHeaderIconsParams): HeaderIconConfig[] => {
  const profileDropdownItems = createProfileDropdownItems(t, handleUserProfile, handleLogout);

  return [
    {
      id: 'help',
      icon: '/assets/dlt_icon_help.svg',
      ariaLabel: t('com_nav_help'),
      showDropdown: false,
      onClick: handleHelpClick,
    },
    {
      id: 'notification',
      icon: '/assets/dlt_icon_notification.svg',
      ariaLabel: t('com_nav_notifications'),
      showDropdown: false,
      onClick: handleNotificationClick,
    },
    {
      id: 'profile',
      icon: '/assets/dlt_icon_profile.svg',
      ariaLabel: t('com_nav_profile'),
      showDropdown: true,
      dropdownItems: profileDropdownItems,
      dropdownHeader: profileDropdownHeader,
      isOpen: isProfileDropdownOpen,
      onOpenChange: setIsProfileDropdownOpen,
    },
  ];
};
