import React from 'react';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from '@librechat/client';

const Logo: React.FC = () => {
  const { t } = useTranslation();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');

  return (
    <div 
      className={`flex items-center gap-2 flex-shrink-0 py-4 pl-6 pr-12 ${
        isSmallScreen ? 'w-80' : 'w-64'
      }`}
    >
      <img
        src="/assets/software_ag_logo.svg"
        alt="Software AG"
        className="h-8 w-auto"
      />
      <div className="h-8 w-px bg-slate-600" />
      <div className="flex flex-col">
        <div className="font-roboto text-lg font-bold text-slate-800">
          {t('com_nav_logo_app_name')}
        </div>
        <div className="font-roboto text-xs font-medium text-slate-800">
          {t('com_nav_logo_tagline')}
        </div>
      </div>
    </div>
  );
};

export default Logo;