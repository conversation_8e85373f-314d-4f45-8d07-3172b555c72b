import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthContext, useLogout } from '~/hooks';
import Logo from './Logo';
import NavTab from './NavTab';
import HeaderIcons from './HeaderIcons';
import { NAV_TABS, createHeaderIconsConfig } from '~/utils/headerUtils';

const Header: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const logout = useLogout();
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);

  // Navigation tabs configuration - only "Chat" tab, always selected
  const navTabs = NAV_TABS;
  const activeTab = 'chat';

  // Header action handlers
  const handleHelpClick = () => {
    console.log('Help clicked');
  };

  const handleNotificationClick = () => {
    console.log('Notification clicked');
  };

  const handleUserProfile = () => {
    console.log('Navigate to user profile');
  };

  // Dynamic header icons configuration
  const headerIcons = useMemo(() => {
    const userName = user?.name || user?.username || user?.email || 'User';

    const profileDropdownHeader = (
      <div className="px-3 py-2 font-roboto text-sm font-medium leading-4 text-[#011F3D]">
        {userName}
      </div>
    );

    return createHeaderIconsConfig({
      t,
      user: user ?? null,
      isProfileDropdownOpen,
      setIsProfileDropdownOpen,
      handleHelpClick,
      handleNotificationClick,
      handleUserProfile,
      handleLogout: logout,
      profileDropdownHeader,
    });
  }, [
    t,
    user,
    isProfileDropdownOpen,
    handleHelpClick,
    handleNotificationClick,
    handleUserProfile,
    logout,
  ]);

  return (
    <header className="flex w-full items-center bg-white shadow-[0_2px_12px_2px_rgba(1,31,61,0.12)]">
      {/* Left Section - Logo with fixed width */}
      <Logo />

      {/* Middle Section - Navigation Tabs (immediately after logo) */}
      <NavTab tabs={navTabs} activeTab={activeTab} onTabChange={() => {}} />

      {/* Right Section - Icons and Profile */}
      <div className="ml-auto flex items-center px-6 py-4">
        <HeaderIcons icons={headerIcons} />
      </div>
    </header>
  );
};

export default Header;
