import React from 'react';
import BaseDropdown from '../BaseDropdown';
import { type DropdownItem } from '~/common/types';
import { type ReactNode } from 'react';

export interface HeaderIconConfig {
  id: string;
  icon: string;
  ariaLabel: string;
  showDropdown: boolean;
  onClick?: () => void;
  dropdownItems?: DropdownItem[];
  dropdownHeader?: ReactNode;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface HeaderIconProps {
  config: HeaderIconConfig;
  className?: string;
}

const HeaderIcon: React.FC<HeaderIconProps> = ({ config, className = '' }) => {
  const baseButtonClass = `flex h-10 w-10 items-center justify-center rounded hover:bg-gray-100 ${className}`;
  
  const iconElement = (
    <img
      src={config.icon}
      alt={config.ariaLabel}
      className="h-6 w-6"
    />
  );

  if (config.showDropdown && config.dropdownItems && config.isOpen !== undefined && config.onOpenChange) {
    const trigger = (
      <button className={baseButtonClass} aria-label={config.ariaLabel}>
        {iconElement}
      </button>
    );

    return (
      <BaseDropdown
        isOpen={config.isOpen}
        onOpenChange={config.onOpenChange}
        trigger={trigger}
        items={config.dropdownItems}
        header={config.dropdownHeader}
      />
    );
  }

  return (
    <button
      onClick={config.onClick}
      className={baseButtonClass}
      aria-label={config.ariaLabel}
    >
      {iconElement}
    </button>
  );
};

export default HeaderIcon;