import React from 'react';
import HeaderIcon, { type HeaderIconConfig } from './HeaderIcon';

interface HeaderIconsProps {
  icons: HeaderIconConfig[];
  className?: string;
}

const HeaderIcons: React.FC<HeaderIconsProps> = ({ icons, className = '' }) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {icons.map((iconConfig) => (
        <HeaderIcon
          key={iconConfig.id}
          config={iconConfig}
        />
      ))}
    </div>
  );
};

export { type HeaderIconConfig };
export default HeaderIcons;