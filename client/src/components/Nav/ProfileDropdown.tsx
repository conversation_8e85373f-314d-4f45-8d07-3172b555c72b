import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthContext, useLogout } from '~/hooks';
import { type DropdownItem } from '~/common/types';
import BaseDropdown from './BaseDropdown';

// Utility function to create profile dropdown items
export const createProfileDropdownItems = (
  t: (key: string) => string,
  handleUserProfile: () => void,
  handleLogout: () => void
): DropdownItem[] => [
  {
    id: 'user-profile',
    icon: '/assets/dlt_icon_profile.svg',
    textKey: t('com_nav_user_profile'),
    onClick: handleUserProfile
  },
  {
    id: 'logout',
    icon: '/assets/dlt_icon_signout.svg',
    textKey: t('com_nav_logout'),
    onClick: handleLogout
  }
];

interface ProfileDropdownProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const ProfileDropdown: React.FC<ProfileDropdownProps> = ({ isOpen, onOpenChange }) => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const logout = useLogout();

  const handleLogout = () => {
    logout();
  };

  const handleUserProfile = () => {
    // TODO: Navigate to user profile page
    console.log('Navigate to user profile');
  };

  const userName = user?.name || user?.username || user?.email || 'User';

  const dropdownItems = createProfileDropdownItems(t, handleUserProfile, handleLogout);

  const trigger = (
    <button className="flex h-10 w-10 items-center justify-center rounded hover:bg-gray-100">
      <img
        src="/assets/dlt_icon_profile.svg"
        alt={t('com_nav_profile')}
        className="h-6 w-6"
      />
    </button>
  );

  const header = (
    <div className="px-3 py-2 font-roboto text-sm font-medium leading-4 text-[#011F3D]">
      {userName}
    </div>
  );

  return (
    <BaseDropdown
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      trigger={trigger}
      items={dropdownItems}
      header={header}
    />
  );
};

export default ProfileDropdown;