import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { type DropdownItem } from '~/common/types';

interface BaseDropdownProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  trigger: ReactNode;
  items: DropdownItem[];
  header?: ReactNode;
  showHeaderSeparator?: boolean;
  contentClassName?: string;
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
  sideOffset?: number;
}

const BaseDropdown: React.FC<BaseDropdownProps> = ({
  isOpen,
  onOpenChange,
  trigger,
  items,
  header,
  showHeaderSeparator = false,
  contentClassName = 'min-w-[200px] rounded-md border border-gray-200 bg-white p-2 shadow-lg',
  align = 'end',
  side = 'bottom',
  sideOffset = 5,
}) => {
  const { t } = useTranslation();
  return (
    <DropdownMenu.Root open={isOpen} onOpenChange={onOpenChange}>
      <DropdownMenu.Trigger asChild>
        {trigger}
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className={contentClassName}
          sideOffset={sideOffset}
          align={align}
          side={side}
        >
          {header && (
            <>
              {header}
              {showHeaderSeparator && (
                <DropdownMenu.Separator className="my-1 h-px bg-gray-200" />
              )}
            </>
          )}

          {items.map((item) => {
            if (item.type === 'separator') {
              return (
                <DropdownMenu.Separator 
                  key={item.id} 
                  className="my-1 h-px bg-gray-200" 
                />
              );
            }

            return (
              <DropdownMenu.Item
                key={item.id}
                className="flex cursor-pointer items-center gap-3 rounded px-3 py-2 font-roboto text-sm font-normal leading-4 text-slate-800 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                onClick={item.onClick}
              >
                {item.icon && (
                  <img
                    src={item.icon}
                    alt=""
                    className="h-4 w-4"
                  />
                )}
                <span>{item.textKey ? t(item.textKey) : ''}</span>
              </DropdownMenu.Item>
            );
          })}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};

export default BaseDropdown;